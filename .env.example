# API Configuration
VITE_API_BASE_URL=http://localhost:3000
VITE_NOTIFICATION_URL=http://localhost:3005

# Development settings
VITE_APP_ENV=development
VITE_DEBUG=true

# Application settings
VITE_APP_NAME=Peta Talenta
VITE_APP_VERSION=1.0.0

# Security settings (optional)
# VITE_ENCRYPTION_KEY=your-encryption-key-here
# VITE_COOKIE_DOMAIN=localhost

# Admin Security Settings
# VITE_ADMIN_ALLOWED_IPS=127.0.0.1,***********/24
# VITE_ADMIN_REQUIRE_2FA=false
# VITE_ADMIN_SESSION_TIMEOUT=28800000
# VITE_ADMIN_MAX_LOGIN_ATTEMPTS=5
